# OMOP Vocabulary files
# Exclude vocabulary files due to their large size and licensing restrictions
/data/vocabulary/downloads/
/data/vocabulary/omop_v5*/
/data/vocabulary/**/*.csv

# FHIR database and generated data
**/data
/data/fhir_data
/data/generated_bundles/
/data/mimic-iv-clinical-database-demo-on-fhir-2.0/

# FHIR analysis files
fhir_analysis_*.txt
fhir_analysis_*_plan.md

# Environment and configuration
.env
.env.local
.env.*.local
.envrc
venv/
env/
.venv/
.python-version

# Agents configuration and context
.augment-guidelines
.cursor

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.AppleDouble
.LSOverride
._*

# Jupyter Notebook
.ipynb_checkpoints

# Temporary files
tmp/
temp/
.temp/
/temp/
/temp/fhir_analysis/
*.bak

# Performance reports (except demo examples)
/performance_reports/
/servers/fhir-server/performance_reports/
# Keep demo example reports
!/demo/demo_streamlit_app/performance_reports/

# Generated bundles (except examples)
/data/generated_bundles/
# Keep small example bundles for testing
!/data/generated_bundles/examples/

# Personal tools and references
docs/notion_references/
